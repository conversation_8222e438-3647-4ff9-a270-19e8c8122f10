<script setup lang="ts">
import { useField } from 'vee-validate'
// @ts-ignore
import Editor from 'ckeditor5-custom-build/build/ckeditor'
const props = defineProps({
  name: {
    type: String,
    default: '',
  },
  modelValue: {
    type: [String, null] as PropType<String | null>,
    required: true,
  },
  placeholder: {
    type: String,
    default: 'Enter',
  },
  label: {
    type: String,
  },
  styleInput: {
    type: Object,
    default: () => ({}),
  },
  classInput: {
    type: [String, Object, Array],
    default: '',
  },
  rules: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['update:modelValue'])
const useConfig = useRuntimeConfig()
const { $auth: auth }: any = useNuxtApp()

class MyUploadAdapter {
  public loader

  constructor(loader: any) {
    // Save Loader instance to update upload progress.
    this.loader = loader
  }

  async upload() {
    const file = await this.loader.file
    const formData = new FormData()
    formData.append('files', file)
    formData.append('resourceType', 'NEWS_CONTENT')

    return new Promise(async (resolve, reject) => {
      await fetch(useConfig.public.apiBase + `/user:upload-files`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${auth.strategy?.token.get()}`,
        },
        body: formData,
      })
        .then((response) => response.json())
        .then((data) => {
          console.log(data, 'data')
          if (data?.data) {
            resolve({ default: data.data[0].url })
          }
        })
    })
  }

  abort() {
    // Reject promise returned from upload() method.
  }
}

function MyCustomUploadAdapterPlugin(editor: any) {
  editor.plugins.get('FileRepository').createUploadAdapter = (loader: any) => {
    // Configure the URL to the upload script in your back-end here!
    return new MyUploadAdapter(loader)
  }
}

// PDF Upload Plugin
function PDFUploadPlugin(editor: any) {
  // Thêm nút PDF upload vào toolbar
  editor.ui.componentFactory.add('pdfUpload', () => {
    const button = editor.ui.componentFactory.create('button')

    button.set({
      label: 'Upload PDF',
      icon: '<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M2 3a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3zm2 1v12h12V4H4zm2 2h8v1H6V6zm0 2h8v1H6V8zm0 2h5v1H6v-1z"/></svg>',
      tooltip: 'Upload PDF file',
    })

    button.on('execute', () => {
      openPDFDialog(editor)
    })

    return button
  })
}

function openPDFDialog(editor: any) {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.pdf'
  input.style.display = 'none'

  input.onchange = async (event: any) => {
    const file = event.target.files[0]
    if (file && file.type === 'application/pdf') {
      await uploadPDF(editor, file)
    }
  }

  document.body.appendChild(input)
  input.click()
  document.body.removeChild(input)
}

async function uploadPDF(editor: any, file: File) {
  try {
    const formData = new FormData()
    formData.append('files', file)
    formData.append('resourceType', 'NEWS_CONTENT')

    const response = await fetch(useConfig.public.apiBase + `/user:upload-files`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${auth.strategy?.token.get()}`,
      },
      body: formData,
    })

    const data = await response.json()

    if (data?.data && data.data[0]?.url) {
      insertPDFLink(editor, data.data[0].url, file.name)
    }
  } catch (error) {
    console.error('PDF upload failed:', error)
  }
}

function insertPDFLink(editor: any, url: string, fileName: string) {
  const model = editor.model

  model.change((writer: any) => {
    const paragraph = writer.createElement('paragraph')
    const linkText = writer.createText(`📄 ${fileName}`)

    writer.append(linkText, paragraph)
    writer.setAttributes({ linkHref: url }, linkText)

    model.insertContent(paragraph)
  })
}

const config = ref({
  extraPlugins: [MyCustomUploadAdapterPlugin, PDFUploadPlugin],
  mediaEmbed: {
    previewsInData: true,
  },
})

const { value, errorMessage, handleBlur } = useField(() => props.name, props.rules, {
  initialValue: props.modelValue as string,
  syncVModel: true,
})

// Thêm nút PDF sau khi component mounted
onMounted(() => {
  nextTick(() => {
    setTimeout(() => {
      addPDFButtonToToolbar()
    }, 2000) // Đợi CKEditor load xong
  })
})

function addPDFButtonToToolbar() {
  const toolbar = document.querySelector('.ck-toolbar__items')
  if (toolbar) {
    // Tạo nút PDF
    const pdfButton = document.createElement('button')
    pdfButton.className = 'ck ck-button ck-off'
    pdfButton.type = 'button'
    pdfButton.innerHTML = `
      <span class="ck-button__icon">
        <svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" style="width: 16px; height: 16px;">
          <path d="M2 3a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3zm2 1v12h12V4H4zm2 2h8v1H6V6zm0 2h8v1H6V8zm0 2h5v1H6v-1z"/>
        </svg>
      </span>
      <span class="ck-button__label">PDF</span>
    `
    pdfButton.title = 'Upload PDF file'
    pdfButton.style.marginLeft = '4px'

    pdfButton.addEventListener('click', (e) => {
      e.preventDefault()
      openPDFDialogDirect()
    })

    toolbar.appendChild(pdfButton)
    console.log('PDF button added to toolbar!')
  } else {
    console.log('Toolbar not found, retrying...')
    setTimeout(addPDFButtonToToolbar, 1000)
  }
}

function openPDFDialogDirect() {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.pdf'
  input.style.display = 'none'

  input.onchange = async (event: any) => {
    const file = event.target.files[0]
    if (file && file.type === 'application/pdf') {
      await uploadPDFDirect(file)
    }
  }

  document.body.appendChild(input)
  input.click()
  document.body.removeChild(input)
}

async function uploadPDFDirect(file: File) {
  try {
    const formData = new FormData()
    formData.append('files', file)
    formData.append('resourceType', 'NEWS_CONTENT')

    const response = await fetch(useConfig.public.apiBase + `/user:upload-files`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${auth.strategy?.token.get()}`,
      },
      body: formData,
    })

    const data = await response.json()

    if (data?.data && data.data[0]?.url) {
      insertPDFLinkDirect(data.data[0].url, file.name)
    }
  } catch (error) {
    console.error('PDF upload failed:', error)
  }
}

function insertPDFLinkDirect(url: string, fileName: string) {
  // Thêm PDF link vào nội dung editor
  const linkHTML = `<p><a href="${url}" target="_blank">📄 ${fileName} (Click để tải xuống)</a></p>`
  const currentContent = value.value || ''
  value.value = currentContent + linkHTML
  console.log('PDF link added to editor content!')
}
</script>

<template>
  <div class="flex flex-col gap-2">
    <label class="text-base font-normal c-black-90" :for="props.name" v-if="label">
      {{ label }} <span class="c-danger" v-if="rules.required">*</span>
    </label>
    <ckeditor
      :id="props.name"
      :class="[classInput, errorMessage ? 'p-invalid' : '']"
      :style="styleInput"
      :editor="Editor"
      :config="config"
      :placeholder="placeholder"
      v-model="value"
      @blur="handleBlur($event, true)" />
    <small class="p-error" v-show="errorMessage">{{ errorMessage || '&nbsp' }}</small>
  </div>
</template>

<style lang="scss" scoped>
:deep {
  // .ck-source-editing-area {
  //   min-height: 300px;
  // }

  .ck-content {
    min-height: 300px;
    // h1,
    // h2,
    // h3,
    // h4,
    // p,
    // b,
    // i,
    // s,
    // u,
    // ul,
    // li,
    // ol,
    // a {
    //   all: revert;
    // }
  }
}
</style>
