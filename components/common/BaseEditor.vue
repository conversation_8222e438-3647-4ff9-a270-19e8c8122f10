<script setup lang="ts">
import { useField } from 'vee-validate'
// @ts-ignore
import Editor from 'ckeditor5-custom-build/build/ckeditor'
const props = defineProps({
  name: {
    type: String,
    default: '',
  },
  modelValue: {
    type: [String, null] as PropType<String | null>,
    required: true,
  },
  placeholder: {
    type: String,
    default: 'Enter',
  },
  label: {
    type: String,
  },
  styleInput: {
    type: Object,
    default: () => ({}),
  },
  classInput: {
    type: [String, Object, Array],
    default: '',
  },
  rules: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['update:modelValue'])
const useConfig = useRuntimeConfig()
const { $auth: auth }: any = useNuxtApp()

class MyUploadAdapter {
  public loader

  constructor(loader: any) {
    // Save Loader instance to update upload progress.
    this.loader = loader
  }

  async upload() {
    const file = await this.loader.file
    const formData = new FormData()
    formData.append('files', file)
    formData.append('resourceType', 'NEWS_CONTENT')

    return new Promise(async (resolve, reject) => {
      await fetch(useConfig.public.apiBase + `/user:upload-files`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${auth.strategy?.token.get()}`,
        },
        body: formData,
      })
        .then((response) => response.json())
        .then((data) => {
          console.log(data, 'data')
          if (data?.data) {
            resolve({ default: data.data[0].url })
          }
        })
    })
  }

  abort() {
    // Reject promise returned from upload() method.
  }
}

function MyCustomUploadAdapterPlugin(editor: any) {
  editor.plugins.get('FileRepository').createUploadAdapter = (loader: any) => {
    // Configure the URL to the upload script in your back-end here!
    return new MyUploadAdapter(loader)
  }
}

const config = ref({
  extraPlugins: [MyCustomUploadAdapterPlugin],
  mediaEmbed: {
    previewsInData: true,
  },
})

const { value, errorMessage, handleBlur } = useField(() => props.name, props.rules, {
  initialValue: props.modelValue as string,
  syncVModel: true,
})

// PDF Upload Function
async function uploadPDFFile(file: File) {
  try {
    const formData = new FormData()
    formData.append('files', file)
    formData.append('resourceType', 'NEWS_CONTENT')

    const response = await fetch(useConfig.public.apiBase + `/user:upload-files`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${auth.strategy?.token.get()}`,
      },
      body: formData,
    })

    const data = await response.json()

    if (data?.data && data.data[0]?.url) {
      return {
        url: data.data[0].url,
        fileName: file.name,
      }
    }
    return null
  } catch (error) {
    console.error('PDF upload failed:', error)
    return null
  }
}

function openPDFDialog() {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.pdf'
  input.style.display = 'none'

  input.onchange = async (event: any) => {
    const file = event.target.files[0]
    if (file && file.type === 'application/pdf') {
      const result = await uploadPDFFile(file)
      if (result) {
        // Insert PDF link into editor content
        const linkHTML = `<p><a href="${result.url}" target="_blank">📄 ${result.fileName}</a></p>`
        const currentContent = value.value || ''
        value.value = currentContent + linkHTML
      }
    }
  }

  document.body.appendChild(input)
  input.click()
  document.body.removeChild(input)
}
</script>

<template>
  <div class="flex flex-col gap-2">
    <label class="text-base font-normal c-black-90" :for="props.name" v-if="label">
      {{ label }} <span class="c-danger" v-if="rules.required">*</span>
    </label>
    <div class="editor-container">
      <ckeditor
        :id="props.name"
        :class="[classInput, errorMessage ? 'p-invalid' : '']"
        :style="styleInput"
        :editor="Editor"
        :config="config"
        :placeholder="placeholder"
        v-model="value"
        @blur="handleBlur($event, true)" />

      <!-- PDF Upload Button -->
      <div class="pdf-upload-section">
        <button @click="openPDFDialog" class="pdf-upload-btn" type="button" title="Upload PDF file">
          <span class="pdf-icon">📄</span>
          <span>Upload PDF</span>
        </button>
      </div>
    </div>
    <small class="p-error" v-show="errorMessage">{{ errorMessage || '&nbsp' }}</small>
  </div>
</template>

<style lang="scss" scoped>
.editor-container {
  position: relative;
}

.pdf-upload-section {
  margin-top: 8px;
  display: flex;
  justify-content: flex-end;
}

.pdf-upload-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  color: #495057;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #e9ecef;
    border-color: #adb5bd;
  }

  &:active {
    background: #dee2e6;
  }
}

.pdf-icon {
  font-size: 16px;
}

:deep {
  // .ck-source-editing-area {
  //   min-height: 300px;
  // }

  .ck-content {
    min-height: 300px;
    // h1,
    // h2,
    // h3,
    // h4,
    // p,
    // b,
    // i,
    // s,
    // u,
    // ul,
    // li,
    // ol,
    // a {
    //   all: revert;
    // }
  }
}
</style>
