<script lang="ts" setup>
import { logoDefault } from '~/assets/images'
import { optionStatus, statusColor, status } from '~/constants'
import { useAppStore } from '~/stores/app'
const { $auth: auth, $dayjs: dayjs } = useNuxtApp()
const route = useRoute()

const query = ref({
  search: (route.query.search as string) || '',
  status: (Number(route.query.status) as number) || '',
  orderType: (route.query.orderType as string) || '',
  username: auth?.user?.username || undefined,
  sapCustomerId: auth?.user?.sapCustomerId || null,
})
const appStore = useAppStore()
const { isCustomer } = useRole()
const listOrder = ref<any>([])
const listType = [
  {
    name: 'Tất cả',
    value: '',
  },
  {
    name: 'Đơn bán',
    value: 'ZOR2',
  },
  {
    name: 'Đơn trả',
    value: 'ZRE1',
  },
  {
    name: 'Đ<PERSON><PERSON> xuất đổi',
    value: 'ZSDH',
  },
  {
    name: 'Đ<PERSON><PERSON> nhập đổi',
    value: 'ZSDH',
  },
  {
    name: '<PERSON><PERSON>i điểm',
    value: 'ZFOC',
  },
]
const expandedRows = ref<string[]>([])

const getData = async () => {
  appStore.isLoading = true
  const url = query.value.sapCustomerId
    ? `ZALP_SALES_ORDER_SRV/ListPOSMSet?$filter=Customer eq '${query.value.sapCustomerId}'&$expand=ListPOSMItemSet`
    : `ZALP_SALES_ORDER_SRV/ListPOSMSet?$filter=UserName eq '${query.value.username}'&$expand=ListPOSMItemSet`
  const { data }: any = await useApi(`sap_api_get`, {
    method: 'GET',
    params: {
      sap_api_url: url,
    },
  })
  listOrder.value = data.value?.d?.results.filter((item: any) => item.OrderType == 'ZFOC') || []
  appStore.isLoading = false
}
getData()

const dataFilter: any = computed(() => {
  const arr = listOrder.value.filter((item: any) => {
    return (
      (query.value.search ? item.OrderNumber.toLowerCase().includes(query.value.search.toLowerCase()) : true) &&
      (query.value.orderType ? item.OrderType == query.value.orderType : true) &&
      (query.value.status ? item.Status == query.value.status : true)
    )
  })
  // const arr = listOrder.value

  return (
    arr.sort((a: any, b: any) => {
      return dayjs(b.OrderDate).valueOf() - dayjs(a.OrderDate).valueOf()
    }) || []
  )
})
watchDebounced(
  () => query.value.search,
  (newValue) => {
    useQueryURL(query.value)
  },
  {
    debounce: 300,
    maxWait: 4000,
  },
)
watchDebounced(
  () => query.value.status,
  (newValue) => {
    useQueryURL(query.value)
  },
  {
    debounce: 300,
    maxWait: 1000,
  },
)
watchDebounced(
  () => query.value.sapCustomerId,
  (newValue) => {
    getData()
  },
  {
    debounce: 300,
    maxWait: 1000,
  },
)

// Add computed property for status counts
const statusCounts = computed(() => {
  const counts: any = {}

  // Initialize counts for all status values
  optionStatus.forEach((item: any) => {
    counts[item.value] = 0
  })

  // Count actual orders
  listOrder.value.forEach((item: any) => {
    if (counts.hasOwnProperty(item.Status)) {
      counts[item.Status]++
    }
  })

  // Add total count for "All" status
  counts['all'] = listOrder.value.length

  return counts
})

const expandAll = () => {
  // expandedRows.value = dataFilter.value.reduce((acc: any, p: any) => (acc[p.OrderNumber] = true) && acc, {})
  expandedRows.value = dataFilter.value
}

// Use sample data
// listOrder.value = sampleOrders
watch(
  () => dataFilter.value,
  () => {
    expandAll()
  },
  {
    immediate: true,
  },
)
</script>

<template>
  <div class="min-h-80vh container mx-a">
    <div class="w-full mx-a fc gap-4">
      <div class="fr jc-sb ai-c">
        <div class="page-heading">Lịch sử đổi điểm</div>
      </div>
      <div class="grid grid-cols-4 gap-4 overflow-hidden">
        <div class="col-span-4">
          <SelectCustomer :showClear="true" v-model:customer="query.sapCustomerId" v-if="!isCustomer" />
        </div>
        <!-- <BaseInputText
          label="Mã đơn hàng"
          name="search"
          class="min-w-300px"
          placeholder="Nhập để tìm kiếm"
          v-model="query.search" /> -->
        <!-- <BaseInputSelect
          label="Sắp xếp"
          v-model="query.orderType"
          :options="listType"
          name="orderType"
          option-label="name"
          option-value="value"
          class="min-w-300px"
          placeholder="Chọn sắp xếp" /> -->
        <!-- <div class="fc jc-fe pb-1px">
          <Button label="Xuất danh sách" severity="primary" class="h-fit"></Button>
        </div> -->
        <!-- <BaseInputSelect
          label="Trạng thái"
          v-model="query.status"
          :options="listStatus"
          name="status"
          option-label="name"
          option-value="value"
          class="min-w-300px"
          placeholder="Chọn status" /> -->
      </div>

      <div class="fc bg-white rounded p-4 gap-4">
        <div class="com-heading">Thông tin khách hàng</div>
        <div class="grid grid-cols-3 gap-4">
          <div class="fc gap-1 row-span-2">
            <BaseAvatar :size="160" type="square" :url="auth.user.avatarUrl ? auth.user.avatarUrl : logoDefault" />
          </div>

          <div class="fc gap-1">
            <div class="text-base font-normal c-black opacity-70 whitespace-nowrap">Tên khách hàng:</div>
            <div class="text-base c-black font-semibold">Minh Đăng BST</div>
          </div>
          <div class="fc gap-1">
            <div class="text-base font-normal c-black opacity-70 whitespace-nowrap">Địa chỉ:</div>
            <div class="text-base c-black font-semibold">123 Đường ABC, Quận XYZ, TP. HCM</div>
          </div>
          <div class="fc gap-1">
            <div class="text-base font-normal c-black opacity-70 whitespace-nowrap">Số điện thoại:</div>
            <div class="text-base c-black font-semibold">0987654321</div>
          </div>
          <div class="fc gap-1">
            <div class="text-base font-normal c-black opacity-70 whitespace-nowrap">Email:</div>
            <div class="text-base c-black font-semibold"><EMAIL></div>
          </div>
          <div class="fc gap-1">
            <div class="text-base font-normal c-black opacity-70 whitespace-nowrap">Khu vực kinh doanh:</div>
            <div class="text-base c-black font-semibold">TP. HCM</div>
          </div>
          <div class="fc gap-1">
            <div class="text-base font-normal c-black opacity-70 whitespace-nowrap">Số điểm tặng:</div>
            <div class="text-base c-black font-semibold">1000</div>
          </div>
          <div class="fc gap-1">
            <div class="text-base font-normal c-black opacity-70 whitespace-nowrap">Số điểm tích lũy:</div>
            <div class="text-base c-black font-semibold">500</div>
          </div>
          <div class="fc gap-1">
            <div class="text-base font-normal c-black opacity-70 whitespace-nowrap">Tổng điểm :</div>
            <div class="text-base c-black font-semibold">1500</div>
          </div>
          <div class="fc gap-1">
            <div class="text-base font-normal c-black opacity-70 whitespace-nowrap">Số điểm đã sử dụng:</div>
            <div class="text-base c-black font-semibold">500</div>
          </div>
          <div class="fc gap-1">
            <div class="text-base font-normal c-black opacity-70 whitespace-nowrap">Số điểm còn lại:</div>
            <div class="text-base c-black font-semibold">1000</div>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1">
        <div class="grid grid-cols-4 gap-4 my-4">
          <!-- <div class="com-heading my-4"></div> -->
          <BaseInputText
            label="Mã đơn hàng"
            name="search"
            class="min-w-300px"
            placeholder="Nhập để tìm kiếm"
            v-model="query.search" />
          <BaseInputCalendar
            label="Từ ngày"
            v-model="query.orderType"
            :options="listType"
            name="orderType"
            option-label="name"
            option-value="value"
            class="min-w-300px"
            placeholder="Chọn thời gian bắt đầu" />
          <BaseInputCalendar
            label="Đến ngày"
            v-model="query.orderType"
            :options="listType"
            name="orderType"
            option-label="name"
            option-value="value"
            class="min-w-300px"
            placeholder="Chọn thời gian kết thúc" />
          <div class="fc jc-fe pb-1px">
            <Button label="Xuất danh sách" severity="primary" class="h-fit"></Button>
          </div>
        </div>
        <div class="bg-white rounded p-4 fc gap-4">
          <div class="fr ai-c w-full gap-2 overflow-x-scroll py-2 px-2 hide-scroll-bar">
            <!-- All status tab -->
            <div
              class="fr ai-c gap-2 bg-gray-100 rounded-lg px-3 py-2 cursor-pointer hover:opacity-80 transition-opacity w-160px min-w-160px"
              @click="query.status = ''"
              :class="{ 'ring-2': query.status === '' }">
              <img src="@/assets/icons/i-status-all.svg" class="w-32px h-32px" alt="" />
              <div class="fc">
                <div class="text-xs font-semibold">Tất cả</div>
                <div class="text-base font-bold text-gray-600">
                  {{ statusCounts['all'] || 0 }}
                </div>
              </div>
            </div>
            <div
              v-for="statusItem in optionStatus"
              :key="statusItem.value"
              class="fr ai-c gap-2 rounded-lg px-3 py-2 cursor-pointer hover:opacity-80 transition-opacity w-160px min-w-160px"
              :style="`background-color: ${statusColor[statusItem.value]}1a`"
              @click="query.status = query.status === statusItem.value ? '' : statusItem.value"
              :class="{ 'ring-2': query.status === statusItem.value }">
              <img :src="statusItem.icon" class="w-32px h-32px" alt="" />
              <div class="fc">
                <div class="text-xs font-semibold">{{ statusItem.name }}</div>
                <div class="text-base font-bold" :style="`color: ${statusColor[statusItem.value]}`">
                  {{ statusCounts[statusItem.value] || 0 }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Order Table -->
        <div class="bg-gray-50 w-full">
          <DataTable
            :value="dataFilter"
            dataKey="OrderNumber"
            rowHover
            scrollable
            :rows="1000"
            :loading="appStore.isLoading"
            scroll-height="calc(100vh - 300px)"
            v-model:expandedRows="expandedRows"
            tableStyle="min-width: 50rem">
            <Column :expander="true" headerStyle="width: 3rem" />

            <Column header="#" style="width: 60px">
              <template #body="slotProps">
                {{ slotProps.index + 1 }}
              </template>
            </Column>

            <Column field="OrderNumber" header="Mã đơn hàng" style="min-width: 150px">
              <template #body="slotProps">
                <nuxt-link
                  :to="
                    slotProps.data.OrderType == 'ZFOC'
                      ? `/history/point/${slotProps.data.OrderNumber}`
                      : `/history/default/${slotProps.data.OrderNumber}`
                  "
                  class="text-primary font-semibold hover:underline">
                  {{ slotProps.data.OrderNumber }}
                </nuxt-link>
              </template>
            </Column>

            <Column field="OrderDate" header="Ngày đặt" style="min-width: 120px">
              <template #body="slotProps">
                <span>{{ useMoment(slotProps.data.OrderDate, 'YYYY-MM-DD') }}</span>
              </template>
            </Column>

            <Column field="DeliveryDate" header="Ngày giao" style="min-width: 120px">
              <template #body="slotProps">
                <span>{{ useMoment(slotProps.data.DeliveryDate, 'YYYY-MM-DD') }}</span>
              </template>
            </Column>

            <Column field="CustomerName" header="Nơi giao" style="min-width: 200px">
              <template #body="slotProps">
                <span>{{ slotProps.data.CustomerName }}</span>
              </template>
            </Column>
            <Column field="TotalPrice" header="Số lượng sản phẩm" style="min-width: 100px">
              <template #body="slotProps">
                {{ slotProps.data.Items?.length || 0 }}
                <!-- {{ useFormatNumber(slotProps.data.TotalPrice) }} -->
              </template>
            </Column>
            <Column field="TotalPrice" header="Doanh thu" style="min-width: 120px">
              <template #body="slotProps">
                <div class="flex justify-content-end">
                  0
                  <!-- {{ useFormatNumber(slotProps.data.TotalPrice) }} -->
                </div>
              </template>
            </Column>
            <Column field="TotalPrice" header="Tổng" style="min-width: 120px">
              <template #body="slotProps">
                <div class="flex justify-content-end">
                  {{ useFormatNumber(slotProps.data.TotalPrice) }}
                  {{ slotProps.data.OrderType === 'ZFOC' ? 'điểm' : 'VNĐ' }}
                </div>
              </template>
            </Column>

            <Column field="StatusText" header="Trạng thái" style="min-width: 120px">
              <template #body="slotProps">
                <span
                  class="text-base font-normal line-clamp-1"
                  :style="`color: ${statusColor[slotProps.data.Status]}`">
                  {{ slotProps.data.StatusText }}
                </span>
              </template>
            </Column>

            <!-- Expansion Template for Items -->
            <template #expansion="slotProps">
              <div class="p-4">
                <div class="text-sm font-semibold text-gray-700 mb-3">Chi tiết sản phẩm:</div>

                <!-- Items Table -->
                <DataTable
                  :value="slotProps.data.ListPOSMItemSet?.results"
                  dataKey="Material"
                  class="p-datatable-sm"
                  tableStyle="min-width: 40rem">
                  <Column header="#" style="width: 50px">
                    <template #body="itemProps">
                      {{ itemProps.index + 1 }}
                    </template>
                  </Column>

                  <Column header="Hình ảnh" style="width: 100px">
                    <template #body="itemProps">
                      <img
                        :src="itemProps.data.image || logoDefault"
                        :alt="itemProps.data.MaterialText"
                        class="w-12 h-12 object-cover rounded border"
                        style="vertical-align: middle" />
                    </template>
                  </Column>

                  <Column field="Material" header="Mã sản phẩm" style="min-width: 150px">
                    <template #body="itemProps">
                      <span class="font-medium">{{ itemProps.data.Material }}</span>
                    </template>
                  </Column>

                  <Column field="MaterialText" header="Tên sản phẩm" style="min-width: 250px">
                    <template #body="itemProps">
                      <span>{{ itemProps.data.MaterialText }}</span>
                    </template>
                  </Column>

                  <Column field="OrderQuantity" header="Số lượng" style="min-width: 100px">
                    <template #body="itemProps">
                      <div class="flex align-items-center gap-2">
                        <span class="font-semibold">{{ useFormatNumber(itemProps.data.OrderQuantity) }}</span>
                      </div>
                    </template>
                  </Column>

                  <Column field="TotalPrice" header="Điểm" style="min-width: 120px">
                    <template #body="itemProps">
                      <div class="flex justify-content-end text-primary">
                        {{ useFormatNumber(itemProps.data.pointx1) }}
                        {{ slotProps.data.OrderType === 'ZFOC' ? 'điểm' : 'VNĐ' }}
                      </div>
                    </template>
                  </Column>
                </DataTable>

                <!-- Items Summary -->
                <!-- <div class="flex justify-content-end font-bold w-full mt-3 p-3 bg-gray-50 rounded">
                  Tổng số sản phẩm: {{ slotProps.data.Items?.length || 0 }} | Tổng điểm:
                  {{ useFormatNumber(slotProps.data.TotalPrice) }}
                  {{ slotProps.data.OrderType === 'ZFOC' ? 'điểm' : 'VNĐ' }}
                </div> -->
              </div>
            </template>

            <template #empty>
              <EmptyState />
            </template>
          </DataTable>
        </div>
      </div>
    </div>
  </div>
</template>
