<template>
  <div class="container mx-auto p-6">
    <h1 class="text-2xl font-bold mb-6">Test PDF Upload</h1>

    <div class="bg-white p-6 rounded-lg shadow-lg">
      <BaseEditor
        v-model="content"
        name="content"
        label="Content with PDF Upload"
        placeholder="Type your content here..."
        class="mb-4" />

      <div class="mt-6">
        <h3 class="text-lg font-semibold mb-2">Preview:</h3>
        <div class="border p-4 rounded bg-gray-50" v-html="content"></div>
      </div>

      <div class="mt-4 p-4 bg-blue-50 rounded">
        <h4 class="font-semibold text-blue-800">Instructions:</h4>
        <ul class="list-disc list-inside text-blue-700 mt-2">
          <li>Look for the "Upload PDF" button below the editor</li>
          <li>Click the button to open file dialog</li>
          <li>Select a PDF file from your computer</li>
          <li>The PDF link will be inserted into the editor content</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const content = ref('<p>Try uploading a PDF file using the button below the editor! 📄</p>')
</script>

<style scoped>
.container {
  max-width: 1200px;
}
</style>
