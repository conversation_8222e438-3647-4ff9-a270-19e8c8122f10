<template>
  <div class="container mx-auto p-6">
    <h1 class="text-2xl font-bold mb-6">Test PDF Upload trong CKEditor</h1>
    
    <div class="bg-white p-6 rounded-lg shadow-lg">
      <BaseEditor
        v-model="content"
        name="content"
        label="Nội dung với PDF Upload"
        placeholder="Nhập nội dung và thử upload PDF..."
        class="mb-4" />
      
      <div class="mt-6">
        <h3 class="text-lg font-semibold mb-2">Preview nội dung:</h3>
        <div class="border p-4 rounded bg-gray-50" v-html="content"></div>
      </div>
      
      <div class="mt-4 p-4 bg-blue-50 rounded">
        <h4 class="font-semibold text-blue-800">Hướng dẫn:</h4>
        <ul class="list-disc list-inside text-blue-700 mt-2">
          <li>Tìm nút PDF (📄) trong toolbar CKEditor</li>
          <li>Click vào nút để mở dialog chọn file</li>
          <li>Chọn file PDF từ máy tính</li>
          <li>File sẽ được upload và link sẽ xuất hiện trong editor</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const content = ref('<p>Hãy thử upload một file PDF bằng cách click vào nút PDF trong toolbar! 📄</p>')
</script>

<style scoped>
.container {
  max-width: 1200px;
}
</style>
