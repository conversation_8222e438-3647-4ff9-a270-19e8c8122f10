<template>
  <div class="container mx-auto p-6">
    <h1 class="text-2xl font-bold mb-6">Test PDF Upload trong CKEditor</h1>
    
    <div class="bg-white p-6 rounded-lg shadow-lg">
      <BaseEditor
        v-model="content"
        name="content"
        label="Nội dung với PDF Upload"
        placeholder="Nhập nội dung và thử upload PDF..."
        class="mb-4"
      />
      
      <div class="mt-6">
        <h3 class="text-lg font-semibold mb-2">Preview nội dung:</h3>
        <div class="border p-4 rounded bg-gray-50" v-html="content"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const content = ref('<p>Hãy thử upload một file PDF bằng cách click vào nút PDF trong toolbar!</p>')
</script>

<style scoped>
.container {
  max-width: 1200px;
}
</style>
