<script lang="ts" setup>
import FormUser from '~/components/user/FormUser.vue'
import { useAppStore } from '~/stores/app'

const appStore = useAppStore()

const { $auth: auth, $dayjs: dayjs } = useNuxtApp()
const isLoading = ref(false)
const listData = ref<any>([])
const totalRecord = ref(0)
const perPage = ref(20)
const confirm = useConfirm()
const toast = useToast()
const filterPanel = ref()
const isShowForm = ref(false)
const formUser = ref<any>({})
const query = ref({
  search: '',
  roles: ['ROLE_CUSTOMER', 'ROLE_CUSTOMER_ORDER', 'ROLE_SALE_ADMIN', 'ROLE_SALE_PERSON'],
  isUpdated: false,
  isLogged: false,
})
const json_fields = {
  Tên: 'fullName',
  Username: 'username',
  SapCustomerID: 'sapCustomerId',
  'Vai trò': 'roles',
  'Trạng thái': 'userStatus',
  'Đăng nhập lần cuối': 'loginLogs',
  'Cập nhật thông tin': 'logs',
}
const dataFilter = computed(() => {
  return listData.value.filter(filterUser)
})

const dataExcel = computed(() => {
  return listData.value.map((el: any) => {
    return {
      fullName: el.fullName,
      username: el.username,
      sapCustomerId: el.sapCustomerId,
      roles: mapRoleName(el.roles[0]),
      userStatus: el.userStatus === 'ACTIVE' ? 'Hoạt động' : 'Không hoạt động',
      loginLogs: mapLastLogin(el.loginLogs),
      logs: mapLastLog(el.logs),
    }
  })
})

const getData = async () => {
  try {
    isLoading.value = true
    const url = encodeURIComponent(`user:list`)
    const { data }: any = await useApi(url, {
      method: 'GET',
    })
    listData.value = data.value?.data || []
    totalRecord.value = data.value?.data.length || 0
    isLoading.value = false
  } catch (error) {
  } finally {
    isLoading.value = false
  }
}
getData()
const mapRoleName = (role: string) => {
  switch (role) {
    case 'ROLE_ADMIN':
      return 'Admin'
    case 'ROLE_SALE':
      return 'Sale Admin'
    case 'ROLE_SALE_PERSON':
      return 'Sale Person'
    case 'ROLE_CUSTOMER':
      return 'Khách hàng'
    case 'ROLE_CUSTOMER_ORDER':
      return 'Khách hàng đặt hàng'
    default:
      return ''
  }
}
const mapLastLogin = (logs: any) => {
  if (logs.length > 0) {
    const lastLogin = logs[logs.length - 1].createdDate
    return dayjs.utc(lastLogin).local().format('DD/MM/YYYY HH:mm:ss')
  } else return ''
}
const mapLastLog = (logs: any) => {
  if (logs.length > 0) {
    const lastLogin = logs[logs.length - 1].createdDate
    return dayjs.utc(lastLogin).local().format('DD/MM/YYYY HH:mm:ss')
  } else return ''
}
const filterUser = (el: any) => {
  if (query.value.search) {
    const keyword = query.value.search.toLowerCase()
    const usernameMatch = el.username.toLowerCase().includes(keyword)
    const fullNameMatch = el.fullName.toLowerCase().includes(keyword)
    if (!usernameMatch && !fullNameMatch) {
      return false
    }
  }
  if (query.value.roles.length > 0) {
    if (!query.value.roles.includes(el.roles[0])) {
      return false
    }
  }

  if (query.value.isUpdated === true) {
    if (!el?.logs || el.logs.length === 0) {
      return false
    }
  }

  if (query.value.isLogged === true) {
    if (!el?.loginLogs || el.loginLogs.length === 0) {
      return false
    }
  }

  return true
}
const editUser = (record: any) => {
  console.log(record, 'record')
  isShowForm.value = true
  formUser.value = { ...record }
}
const removeUser = (record: any) => {
  confirm.require({
    message: 'Bạn có chắc chắn muốn xóa người dùng này không?',
    header: 'Xác nhận',
    icon: 'pi pi-exclamation-triangle',
    rejectLabel: 'Hủy',
    acceptLabel: 'Xóa',
    acceptClass: 'p-button-danger',
    rejectClass: 'p-button-help',
    accept: async () => {
      const url = encodeURIComponent(`user:delete`) + `/${record.id}`
      const { data }: any = await useApi(url, {
        method: 'DELETE',
      })
      if (data.value?.data) {
        getData()
        toast.add({ severity: 'success', summary: 'Thông báo', detail: 'Xóa thành công', life: 3000 })
      }
    },
  })
}
const handleSubmit = async (form: any) => {
  try {
    isLoading.value = true
    const url = encodeURIComponent(form.userType === 'INTERNAL_USER' ? `user:update_internal` : 'user:update_internal')
    const formData = {
      id: form.id,
      firstName: form.firstName,
      // lastName: form.lastName,
      username: form.username,
      userStatus: form.userStatus,
      password: form.password,
      email: form.email || null,
      phone: form.phone,
      address: form.address,
      page: form.page,
      language: form.language,
      roles: form.roles,
    }
    const { data }: any = await useApi(url, {
      method: 'PUT',
      body: formData,
    })
    if (data.value?.data) {
      isShowForm.value = false
      formUser.value = {}
      getData()
      toast.add({ severity: 'success', summary: 'Thông báo', detail: 'Cập nhật thành công', life: 3000 })
    }
  } catch (error) {
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <div class="container mx-a fc gap-4">
    <div class="bg-white fc gap-4 p-4 rounded w-full">
      <div class="com-heading">Danh sách người dùng</div>
      <div class="fr ai-c jc-fe gap-4">
        <BaseInputText placeholder="Tìm kiếm" class="flex-1" v-model="query.search" />
        <Button class="w-160px fr jc-c" @click="filterPanel.toggle($event)">
          <div class="fr ai-c gap-2"><img src="~/assets/icons/i-filter-white.svg" alt="" /> Bộ lọc</div>
        </Button>
        <OverlayPanel class="min-w-[320px] [&>.p-overlaypanel-content]:p-0" ref="filterPanel">
          <div class="grid grid-cols-1 p-4 gap-4">
            <div class="fr ai-c gap-4">
              <Checkbox v-model="query.roles" inputId="ROLE_CUSTOMER" name="roles" value="ROLE_CUSTOMER" />
              <label class="text-base font-normal c-black-90" for="ingredient3">Khách hàng</label>
            </div>
            <div class="fr ai-c gap-4">
              <Checkbox v-model="query.roles" inputId="ROLE_CUSTOMER_ORDER" name="roles" value="ROLE_CUSTOMER_ORDER" />
              <label class="text-base font-normal c-black-90" for="ingredient3">Khách hàng đặt hàng</label>
            </div>
            <div class="fr ai-c gap-4">
              <Checkbox v-model="query.roles" inputId="ROLE_SALE_ADMIN" name="roles" value="ROLE_SALE_ADMIN" />
              <label class="text-base font-normal c-black-90" for="ingredient3">Sale Admin</label>
            </div>
            <div class="fr ai-c gap-4">
              <Checkbox v-model="query.roles" inputId="ROLE_SALE_PERSON" name="roles" value="ROLE_SALE_PERSON" />
              <label class="text-base font-normal c-black-90" for="ingredient3">Sale Person</label>
            </div>
            <BaseCheckbox name="isLogged" label="Đã đăng nhập" v-model="query.isUpdated" />
            <BaseCheckbox name="isUpdated" label="Đã cập nhật" v-model="query.isLogged" />
          </div>
        </OverlayPanel>
        <download-excel :data="dataExcel" :json_fields="json_fields" worksheet="My Worksheet" name="users.xls">
          <Button label="Xuất danh sách" severity="primary" class="w-160px fr jc-c" />
        </download-excel>
        <!-- <Button label="Thêm người dùng" severity="primary" class="w-160px fr jc-c" /> -->
      </div>
      <div class="grid grid-cols-2">
        <DataTable
          :value="dataFilter"
          dataKey="_id"
          class="w-full col-span-2"
          rowHover
          :rows="perPage"
          paginator
          scrollable
          :rows-per-page-options="[20, 50, 100]"
          :total-records="totalRecord"
          scroll-height="calc(100vh - 270px)"
          :loading="isLoading">
          <Column header="#" :frozen="true" alignFrozen="left">
            <template #body="slotProps">
              {{ slotProps.index + 1 }}
            </template>
          </Column>

          <Column field="fullName" header="Tên" style="min-width: 200px" :frozen="true" alignFrozen="left"> </Column>
          <Column field="username" header="Username" style="min-width: 200px"> </Column>
          <Column field="sapCustomerId" header="SapCustomerID" style="min-width: 180px"> </Column>
          <Column field="roles" header="Vai trò" style="min-width: 150px">
            <template #body="slotProps">
              {{ mapRoleName(slotProps.data.roles[0]) }}
            </template>
          </Column>
          <Column field="userStatus" header="Trạng thái" style="min-width: 180px">
            <template #body="slotProps">
              {{ slotProps.data.userStatus === 'ACTIVE' ? 'Hoạt động' : 'Không hoạt động' }}
            </template>
          </Column>
          <Column field="loginLogs" header="Đăng nhập lần cuối" style="min-width: 200px">
            <template #body="slotProps">
              {{ mapLastLog(slotProps.data.logs) }}
            </template>
          </Column>
          <Column field="loginLogs" header="Cập nhật thông tin" style="min-width: 200px">
            <template #body="slotProps">
              {{ mapLastLogin(slotProps.data.loginLogs) }}
            </template>
          </Column>
          <Column field="action" header="Hành động" style="min-width: 120px" :frozen="true" alignFrozen="right">
            <template #body="slotProps">
              <div class="fr ai-c gap-4">
                <img
                  src="~/assets/icons/i-trash-red.svg"
                  alt=""
                  class="cursor-pointer"
                  @click="removeUser(slotProps.data)" />
                <img src="~/assets/icons/i-edit.svg" class="cursor-pointer" @click="editUser(slotProps.data)" alt="" />
              </div>
            </template>
          </Column>

          <template #empty>
            <EmptyState />
          </template>
        </DataTable>
      </div>
    </div>
    <BaseDialog
      v-model:visible="isShowForm"
      :title="`${formUser.id ? 'Chỉnh sửa' : 'Thêm'} người dùng`"
      :style="{ width: '800px' }">
      <FormUser v-model:form="formUser" @submit="handleSubmit" @cancel=";(isShowForm = false), (formUser = {})" />
    </BaseDialog>
  </div>
</template>
