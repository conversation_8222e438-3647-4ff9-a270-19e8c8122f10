# Hướng dẫn Upload PDF trong CKEditor

## Tính năng đã thêm

Tôi đã thêm tính năng upload PDF vào CKEditor của bạn với các cải tiến sau:

### 1. Plugin PDF Upload
- **Nút PDF Upload**: Thêm nút "📄" vào toolbar của CKEditor
- **Upload tự động**: Click nút sẽ mở dialog chọn file PDF
- **Chèn link**: <PERSON><PERSON> <PERSON>hi upload thành công, tự động chèn link PDF vào nội dung

### 2. Cách hoạt động

1. **Click nút PDF** trong toolbar CKEditor (biểu tượng 📄)
2. **Chọn file PDF** từ máy tính của bạn
3. **File được upload** lên server thông qua API `/user:upload-files`
4. **Link PDF được chèn** vào nội dung với format: `📄 [Tên file] (Click để tải xuống)`

### 3. Các file đã được chỉnh sửa

#### `components/common/BaseEditor.vue`
- Thêm plugin `createPDFUploadPlugin`
- Thêm functions: `openPDFFileDialog`, `uploadPDFFile`, `insertPDFLink`
- Cập nhật toolbar config để bao gồm nút `pdfUpload`
- Cải thiện error handling cho upload

#### `pages/test-pdf-upload.vue` (Mới)
- Trang test để thử nghiệm tính năng upload PDF
- Có preview nội dung real-time

### 4. Cách sử dụng

```vue
<template>
  <BaseEditor
    v-model="content"
    name="content"
    label="Nội dung"
    placeholder="Nhập nội dung và upload PDF..."
  />
</template>

<script setup>
const content = ref('')
</script>
```

### 5. Toolbar mới

Toolbar hiện tại bao gồm:
```
undo | redo | heading | bold | italic | underline | strikethrough | 
fontBackgroundColor | fontColor | bulletedList | numberedList | alignment |
imageUpload | pdfUpload | mediaEmbed | link | blockQuote | insertTable | sourceEditing
```

### 6. Kiểm tra tính năng

1. Chạy project: `npm run dev`
2. Truy cập: `/test-pdf-upload`
3. Click nút PDF trong CKEditor
4. Chọn file PDF và test upload

### 7. Lưu ý kỹ thuật

- **File type validation**: Chỉ chấp nhận file `.pdf`
- **API endpoint**: Sử dụng endpoint hiện có `/user:upload-files`
- **Resource type**: `NEWS_CONTENT`
- **Authentication**: Sử dụng Bearer token từ auth store
- **Error handling**: Console log lỗi nếu upload thất bại

### 8. Tùy chỉnh thêm

Nếu bạn muốn tùy chỉnh thêm:

#### Thay đổi icon PDF:
```javascript
icon: '<svg>...</svg>' // Thay đổi trong createPDFUploadPlugin
```

#### Thay đổi format hiển thị:
```javascript
const pdfIcon = writer.createText('📄 ')
const linkText = writer.createText(fileName)
const downloadText = writer.createText(' (Click để tải xuống)')
```

#### Thêm validation file size:
```javascript
if (file.size > 10 * 1024 * 1024) { // 10MB
  alert('File quá lớn!')
  return
}
```

### 9. Troubleshooting

**Nếu nút PDF không hiển thị:**
- Kiểm tra console có lỗi không
- Đảm bảo CKEditor build đã được rebuild
- Kiểm tra toolbar config

**Nếu upload thất bại:**
- Kiểm tra API endpoint
- Kiểm tra authentication token
- Kiểm tra network tab trong DevTools

**Nếu link không hoạt động:**
- Kiểm tra URL được trả về từ API
- Kiểm tra target="_blank" attribute
